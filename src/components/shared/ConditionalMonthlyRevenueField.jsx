import React, { useState, useEffect } from "react";
import { useController, useWatch, useFormContext } from "react-hook-form";
import { SelectField } from "./SelectField.jsx";
import { CurrencyField } from "./CurrencyField.jsx";
import { validationSchema } from "../../utils/validationSchema";

const revenueOptions = [
  { value: "0-10000", label: "$0 - $10,000" },
  { value: "10000-25000", label: "$10,000 - $25,000" },
  { value: "25000-100000", label: "$25,000 - $100,000" },
  { value: "100000-250000", label: "$100,000 - $250,000" },
  { value: "250000-1000000", label: "$250,000 - $1,000,000" },
  { value: "1000000+", label: "$1,000,000+" },
];

export const ConditionalMonthlyRevenueField = ({ control }) => {
  const { setValue } = useFormContext();

  const monthlyRevenue = useWatch({
    control,
    name: "monthlyRevenue",
  });

  const annualRevenue = useWatch({
    control,
    name: "annualRevenue",
  });

  const [showAnnualRevenue, setShowAnnualRevenue] = useState(false);

  useEffect(() => {
    if (monthlyRevenue === "0-10000") {
      setShowAnnualRevenue(true);
    } else {
      setShowAnnualRevenue(false);
      // Clear annual revenue when not needed
      if (annualRevenue) {
        setValue("annualRevenue", "");
      }
    }
  }, [monthlyRevenue, setValue, annualRevenue]);

  useEffect(() => {
    if (showAnnualRevenue && annualRevenue >= 120000) {
      // Auto-select 10000-25000 and hide the annual revenue field
      setValue("monthlyRevenue", "10000-25000");
      setShowAnnualRevenue(false);
    }
  }, [annualRevenue, showAnnualRevenue, setValue]);

  return (
    <div className="space-y-4">
      <SelectField
        name="monthlyRevenue"
        label="Monthly Business Revenue"
        control={control}
        options={revenueOptions}
        placeholder="Select monthly revenue..."
        rules={validationSchema.monthlyRevenue}
        data-hj-allow
      />

      {showAnnualRevenue && (
        <div className="ml-4 p-4 bg-gray-50 rounded-md border-l-4 border-blue-500">
          <CurrencyField
            name="annualRevenue"
            label="Confirm your estimated total yearly sales"
            control={control}
            rules={validationSchema.annualRevenue}
            placeholder="$0"
            data-hj-allow
          />
          <p className="text-sm text-gray-600 mt-2">
            Please enter your estimated total yearly sales to continue.
          </p>
        </div>
      )}
    </div>
  );
};
